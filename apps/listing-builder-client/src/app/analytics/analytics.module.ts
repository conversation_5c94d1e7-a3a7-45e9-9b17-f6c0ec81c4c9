import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AnalyticsComponent } from './analytics.component';
import { AnalyticsRouting } from './analytics.routing';
import { GoogleInsightsModule } from '../google-insights/google-insights.module';
import { SharedModule } from '../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { MatTab, MatTabGroup, MatTabLabel } from '@angular/material/tabs';
import { MatIcon } from '@angular/material/icon';
import { BingInsightsModule } from '../bing-insights/bing-insights.module';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';

@NgModule({
  declarations: [AnalyticsComponent],
  imports: [
    CommonModule,
    AnalyticsRouting,
    GoogleInsightsModule,
    SharedModule,
    TranslateModule,
    MatTabGroup,
    MatTab,
    MatIcon,
    MatTab<PERSON>abel,
    BingInsightsModule,
    GalaxyLoadingSpinnerModule,
  ],
  exports: [AnalyticsComponent],
})
export class AnalyticsModule {}
