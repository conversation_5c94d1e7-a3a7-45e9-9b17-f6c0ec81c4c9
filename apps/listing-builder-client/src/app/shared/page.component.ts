import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { FeatureFlagMultiResponse } from '@vendasta/businesses';
import {
  Breadcrumbs,
  Filters,
  VaProductNavActionButton,
  VaProductNavPageInformation,
  VaProductNavService,
} from '@vendasta/uikit';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { FEATURE_FLAGS_TOKEN } from '../app.module';
import { AppConfig, AppConfigService } from '../core';

@Component({
  selector: 'app-page',
  template: `
    <glxy-page>
      <glxy-page-toolbar>
        <glxy-page-title>
          {{ _pageTitle }}
        </glxy-page-title>
        <glxy-page-actions>
          <app-google-insights-filters *ngIf="displayGoogleInsightsTitleActions"></app-google-insights-filters>
        </glxy-page-actions>
      </glxy-page-toolbar>
      <glxy-page-wrapper [widthPreset]="widthPreset">
        <div class="page">
          <ng-content></ng-content>
          <div class="footer">
            <p>
              {{ 'COMMON.DISCLAIMER' | translate }}
            </p>
          </div>
        </div>
      </glxy-page-wrapper>
    </glxy-page>
  `,
  styleUrls: ['./page.component.scss'],
  standalone: false,
})
export class PageComponent implements OnInit, OnDestroy {
  @Input() breadcrumbs: Breadcrumbs[];
  @Input()
  set pageTitle(title: string) {
    this._pageTitle = title;
    this.calculateBreadcrumbs();
    const googleInsightsTitle = this.translate.instant('ANALYTICS.NAME');
    const analyticsTitle = this.translate.instant('ANALYTICS.NAME');

    if (title === googleInsightsTitle || title === analyticsTitle) {
      this.displayGoogleInsightsTitleActions = true;
    }
  }
  @Input()
  set pageFilters(filters: Filters) {
    this.navService.setFilters(filters);
  }
  @Input()
  set pageInfo(info: VaProductNavPageInformation) {
    this.navService.setInfoSidebar(info);
  }
  @Input()
  set primaryActionButton(button: VaProductNavActionButton) {
    this.displayListingProfileTab$.subscribe((lp) => {
      if (!lp) {
        this.navService.setPrimaryActionButton(button);
      }
    });
  }

  @Input() widthPreset: 'narrow' | 'default' | 'wide' | 'full' = 'wide';

  @Input() isSearchable;
  @Input()
  set searchTerm(searchTerm: string) {
    if (this.isSearchable && searchTerm === null) {
      searchTerm = '';
    }
    this.navService.setSearch(searchTerm);
  }
  @Output() search: EventEmitter<string> = new EventEmitter<string>();

  _pageTitle: string;
  config$: Observable<AppConfig>;
  subscriptions: Subscription[] = [];
  displayListingProfileTab$: Observable<boolean>;
  displayGoogleInsightsTitleActions: boolean;

  constructor(
    @Inject(FEATURE_FLAGS_TOKEN) private featureFlags$: Observable<FeatureFlagMultiResponse>,
    private appConfigService: AppConfigService,
    private navService: VaProductNavService,
    private router: Router,
    private translate: TranslateService,
  ) {
    this.router.events.subscribe(() => this.calculateBreadcrumbs());
    this.config$ = this.appConfigService.appConfig$;
    this.displayListingProfileTab$ = this.featureFlags$.pipe(map((flags) => flags['lb_display_listing_profile_tab']));
  }

  ngOnInit() {
    this.calculateBreadcrumbs();
    this.subscriptions.push(this.navService.search$.subscribe((search) => this.search.emit(search)));
  }

  ngOnDestroy() {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  calculateBreadcrumbs() {
    if (this.breadcrumbs) {
      this.navService.setBreadcrumbs(this.breadcrumbs);
    } else if (this._pageTitle) {
      this.navService.setBreadcrumbs([{ text: this._pageTitle }]);
    } else {
      this.navService.setBreadcrumbs([{ text: '' }]);
    }
  }
}
