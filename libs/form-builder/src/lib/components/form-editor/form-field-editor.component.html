<div *ngIf="field !== null">
  <form [formGroup]="fieldEditorForm">
    <section class="field-editor">
      <div class="title-bar">
        <!-- label stores HTML for Rich Text Element, which we don't want to display, so display the field type label instead -->
        <div
          *ngIf="fieldType === fieldTypes.FIELD_TYPE_RICH_TEXT_ELEMENT; else genericFieldLabel"
          class="field-editor-title"
        ></div>
        <ng-template #genericFieldLabel>
          <div class="field-editor-title">Edit {{ field.formField.label | translate }}</div>
        </ng-template>
        <button mat-icon-button aria-label="cancel editing form field" (click)="cancelEdits()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <glxy-form-field>
        <glxy-label>{{ 'FIELD_OPTIONS.FIELD_TYPE' | translate }}</glxy-label>
        <div *ngIf="field | canChangeType; else fieldTypeSection">
          <mat-select [formControl]="fieldEditorForm.controls.fieldType">
            <mat-option [value]="fieldTypes.FIELD_TYPE_STRING">
              {{ 'GENERIC_FIELD_TYPES.STRING' | translate }}
            </mat-option>
            <mat-option [value]="fieldTypes.FIELD_TYPE_DROPDOWN">
              {{ 'GENERIC_FIELD_TYPES.DROPDOWN' | translate }}
            </mat-option>
            <mat-option [value]="fieldTypes.FIELD_TYPE_RADIO">
              {{ 'GENERIC_FIELD_TYPES.RADIO' | translate }}
            </mat-option>
            <mat-option [value]="fieldTypes.FIELD_TYPE_TEXT_AREA">
              {{ 'GENERIC_FIELD_TYPES.TEXT_AREA' | translate }}
            </mat-option>
          </mat-select>
        </div>
        <ng-template #fieldTypeSection>
          <span class="field-type-value">{{ fieldTypeLabel | translate }}</span>
          <form-builder-field-type-hint
            *ngIf="fieldType === fieldTypes.FIELD_TYPE_BUSINESS_SEARCH"
            [hintContent]="'CRM_COMPANY_FIELD_TYPES.BUSINESS_SEARCH_HINT'"
          ></form-builder-field-type-hint>
        </ng-template>
      </glxy-form-field>
    </section>

    <mat-divider></mat-divider>

    <ng-container *ngIf="(fieldType$$ | async) === fieldTypes.FIELD_TYPE_RICH_TEXT_ELEMENT; else genericFieldEditor">
      <section class="field-editor">
        <!--
        We need to condition here on isActive so that the rich-text-editor is reinitialized when it leaves and re-enters the view
        Otherwise the editor won't work when say, switching between mat-tab options
      -->
        <glxy-rich-text-editor
          class="rich-text-editor"
          *ngIf="isActive === true"
          elementId="rich-text-element-content"
          [formControl]="fieldEditorForm.controls.label"
          [enableThemes]="true"
          plugins="code link lists"
          [toolbar]="[
            'styleselect | bold italic link unlink | alignleft aligncenter alignright | bullist numlist | code removeformat',
          ]"
        ></glxy-rich-text-editor>
        <glxy-error *ngIf="labelErrorMsg$ | async as labelErrorMsg">
          {{ labelErrorMsg | translate }}
        </glxy-error>
      </section>
    </ng-container>
    <ng-template #genericFieldEditor>
      <section class="field-editor">
        <glxy-form-field [required]="isLabelRequired$ | async">
          <glxy-label>{{ 'FIELD_OPTIONS.LABEL' | translate }}</glxy-label>
          <input
            type="text"
            matInput
            [formControl]="fieldEditorForm.controls.label"
            data-cy="custom-form-label-input"
          />
          <glxy-error *ngIf="labelErrorMsg$ | async as labelErrorMsg">
            {{ labelErrorMsg | translate }}
          </glxy-error>
        </glxy-form-field>

        <glxy-form-field *ngIf="showPlaceholder === true">
          <glxy-label>{{ 'FIELD_OPTIONS.PLACEHOLDER_TEXT' | translate }}</glxy-label>
          <input
            type="text"
            matInput
            [formControl]="fieldEditorForm.controls.placeholder"
            [placeholder]="'FIELD_OPTIONS.PLACEHOLDER_TEXT' | translate"
          />
        </glxy-form-field>

        <glxy-form-field *ngIf="showOptions === true">
          <glxy-label>{{ 'FIELD_OPTIONS.OPTIONS' | translate }}</glxy-label>
          <form-builder-options
            [options]="options"
            (optionsChange)="onOptionsChanged(field, $event)"
          ></form-builder-options>
        </glxy-form-field>

        <glxy-form-field
          [ngClass]="
            (fieldType$$ | async) === fieldTypes.FIELD_TYPE_BOOLEAN
              ? 'default-value-field-boolean'
              : 'default-value-field'
          "
          [ngSwitch]="fieldType$$ | async"
          [required]="isDefaultRequired$ | async"
        >
          <glxy-label>{{ 'FIELD_OPTIONS.DEFAULT' | translate }}</glxy-label>
          <glxy-error *ngIf="defaultErrorMsg$ | async as defaultErrorMsg">
            {{ defaultErrorMsg | translate }}
          </glxy-error>

          <mat-select
            *ngSwitchCase="fieldTypes.FIELD_TYPE_DROPDOWN"
            [formControl]="fieldEditorForm.controls.defaultValue"
            data-cy="dropdown-options-select"
          >
            <!--
          no label added because it would be worthless https://github.com/angular/components/issues/17980
        -->
            <mat-option [value]="undefined"></mat-option>
            <mat-option *ngFor="let option of field.options" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>

          <mat-radio-group
            *ngSwitchCase="fieldTypes.FIELD_TYPE_RADIO"
            [formControl]="fieldEditorForm.controls.defaultValue"
            data-cy="radio-options-group"
          >
            <mat-radio-button [value]="undefined">
              {{ 'FIELD_OPTIONS.NO_DEFAULT_OPTION' | translate }}
            </mat-radio-button>
            <mat-radio-button *ngFor="let option of field.options" [value]="option.value">
              {{ option.label }}
            </mat-radio-button>
          </mat-radio-group>

          <mat-radio-group
            *ngSwitchCase="fieldTypes.FIELD_TYPE_BOOLEAN"
            [formControl]="fieldEditorForm.controls.defaultValue"
          >
            <mat-radio-button [value]="false">
              {{ 'FIELD_OPTIONS.CHECKBOX_DEFAULT_VALUE.NOT_SELECTED' | translate }}
            </mat-radio-button>
            <mat-radio-button [value]="true">
              {{ 'FIELD_OPTIONS.CHECKBOX_DEFAULT_VALUE.SELECTED' | translate }}
            </mat-radio-button>
          </mat-radio-group>

          <glxy-tags-input
            *ngSwitchCase="fieldTypes.FIELD_TYPE_TAG"
            [availableTags]="tagsFilteredBySearchText()"
            (searchText)="tagsSearchTextChanged($event)"
            [formControl]="fieldEditorForm.controls.defaultValue"
            [uniqueTags]="true"
            [autocompleteOnly]="false"
            [maxTagLength]="50"
            [maxTags]="50"
            [addOnBlur]="true"
          ></glxy-tags-input>

          <input
            *ngSwitchDefault
            [type]="defaultControlType"
            matInput
            [formControl]="fieldEditorForm.controls.defaultValue"
            data-cy="default-value-field"
          />
        </glxy-form-field>

        <glxy-form-field *ngIf="(fieldType$$ | async) === fieldTypes.FIELD_TYPE_PHONE">
          <glxy-label>{{ 'FIELD_OPTIONS.PHONE_COUNTRY_CODE' | translate }}</glxy-label>
          <mat-select formControlName="phoneCountryCode">
            <mat-select-trigger>
              <p
                *ngIf="selectedCountry() as selectedCountry; else invalidCountryTrigger"
                [attr.aria-label]="selectedCountry.countryName"
              >
                {{ selectedCountry.callingCode }} ({{ selectedCountry.flag }} {{ selectedCountry.countryName }})
              </p>
            </mat-select-trigger>
            <!-- if this ever happens something is wrong -->
            <ng-template #invalidCountryTrigger>Invalid</ng-template>
            <mat-option>
              <ngx-mat-select-search
                [placeholderLabel]="'MODEL_DRIVEN_FORM.PHONE.SEARCH_COUNTRY_LABEL' | translate"
                [noEntriesFoundLabel]="'MODEL_DRIVEN_FORM.PHONE.SEARCH_COUNTRY_NO_RESULTS' | translate"
                [formControl]="searchCountryControl"
              ></ngx-mat-select-search>
            </mat-option>
            <mat-option
              class="crm-phone-country-option"
              *ngFor="let option of filteredCountryOptions$ | async"
              [value]="option.countryCode"
            >
              <span class="crm-phone-country-option-calling-code">{{ option.callingCode }}</span>
              <span class="crm-phone-country-option-display">({{ option.flag }} {{ option.countryName }})</span>
            </mat-option>
          </mat-select>
        </glxy-form-field>

        <glxy-form-field [bottomSpacing]="'none'">
          <mat-checkbox [formControl]="fieldEditorForm.controls.required" data-cy="required-field-checkbox">
            {{ 'FIELD_OPTIONS.REQUIRED' | translate }}
          </mat-checkbox>
        </glxy-form-field>

        <glxy-form-field [bottomSpacing]="'none'">
          <mat-checkbox [formControl]="fieldEditorForm.controls.hidden" data-cy="hidden-field-checkbox">
            {{ 'FIELD_OPTIONS.HIDDEN' | translate }}
          </mat-checkbox>
        </glxy-form-field>

        <glxy-form-field class="dynamically-populated-field">
          <mat-checkbox [formControl]="fieldEditorForm.controls.dynamic" data-cy="dynamically-populated-field">
            {{ 'FIELD_OPTIONS.POPULATE_DYNAMICALLY.CHECKMARK' | translate }}
          </mat-checkbox>
        </glxy-form-field>
        <glxy-form-field [required]="showQueryParam$ | async" *ngIf="showQueryParam$ | async">
          <glxy-label>{{ 'FIELD_OPTIONS.POPULATE_DYNAMICALLY.QUERY_PARAM' | translate }}</glxy-label>
          <input
            type="text"
            placeholder="{{ 'FIELD_OPTIONS.POPULATE_DYNAMICALLY.QUERY_PARAM' | translate }}"
            matInput
            [formControl]="fieldEditorForm.controls.prefillUrl"
            data-cy="dynamically-query-param-field"
          />
          <glxy-hint>{{ 'FIELD_OPTIONS.POPULATE_DYNAMICALLY.QUERY_PARAM_HINT' | translate }}</glxy-hint>
          <glxy-error *ngIf="queryParamErrorMsg$ | async as error">{{ error | translate }}</glxy-error>
        </glxy-form-field>
      </section>
    </ng-template>
  </form>
</div>
