// we don't want to re-order the fields here because it's easier to read with the interfaces separated
/* eslint @typescript-eslint/member-ordering: 0 */

import { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import {
  booleanAttribute,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  Self,
  ViewChild,
} from '@angular/core';
import { ControlValueAccessor, FormControl, NgControl } from '@angular/forms';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatFormFieldControl } from '@angular/material/form-field';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject, map } from 'rxjs';

@Component({
  selector: 'glxy-tags-input',
  styleUrls: ['./tags.component.scss'],
  templateUrl: './tags.component.html',
  standalone: false,
})
export class TagsComponent implements OnInit, OnDestroy, MatFormFieldControl<string[]>, ControlValueAccessor {
  @ViewChild('searchInput') searchInput?: ElementRef<HTMLInputElement>;

  private static nextId = 0;

  @Output() searchText = new EventEmitter<string>();
  @Input() maxTagLength = 50;
  @Input() maxTags: number | null = null;

  _availableTags: string[] = [];
  @Input() set availableTags(tags: string[]) {
    this._availableTags = tags;
  }
  get availableTags(): string[] {
    if (this.uniqueTags) {
      return this._availableTags.filter((tag) => !this.chosenTagsControl.value.includes(tag));
    }
    return this._availableTags;
  }

  @Input({ transform: booleanAttribute }) uniqueTags = true;
  @Input({ transform: booleanAttribute }) autocompleteOnly = false;
  @Input({ transform: booleanAttribute }) addOnBlur = false;

  chosenTagsControl: FormControl<string[]> = new FormControl<string[]>([], { nonNullable: true });
  searchControl: FormControl<string> = new FormControl<string>('', { nonNullable: true });
  separatorKeysCodes: number[] = [ENTER, COMMA];

  maxTagLengthReached$: Observable<boolean> = this.searchControl.valueChanges.pipe(
    map((searchTerm) => searchTerm.length >= this.maxTagLength),
  );
  maxTagLengthReachedMessage: string;

  constructor(
    @Optional() @Self() public ngControl: NgControl,
    private _elementRef: ElementRef<HTMLElement>,
    private readonly translate: TranslateService,
  ) {
    if (this.ngControl != null) {
      // Setting the value accessor directly (instead of using
      // the providers) to avoid running into a circular import.
      this.ngControl.valueAccessor = this;
    }

    this.maxTagLengthReachedMessage = this.translate.instant('GALAXY.TAGS.STRING_LIMIT_HINT', {
      maxLength: this.maxTagLength || 0,
    });
  }

  ngOnInit(): void {
    this.searchControl.valueChanges.subscribe((searchTerm) => {
      this.searchText.emit(searchTerm);
    });
  }

  ngOnDestroy(): void {
    this.stateChanges.complete();
  }

  /**
   * TagsComponent-specific fields
   */
  private addTag(tag: string): void {
    if (this.uniqueTags && this.chosenTagsControl.value.includes(tag)) {
      return;
    }

    if (this.autocompleteOnly && !this._availableTags.includes(tag)) {
      return;
    }

    const tags = this.chosenTagsControl.value || [];
    this.value = [...tags, tag];
    this.onChange(this.value);
  }

  private touchControls(): void {
    this.chosenTagsControl.markAsDirty();
    this.chosenTagsControl.markAsTouched();
    this.chosenTagsControl.updateValueAndValidity();
    this.searchControl.setValue('');
    this.searchControl.updateValueAndValidity();
    this.onTouched();
  }

  addNewTagByTokenEnd(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (value) {
      this.addTag(value);
    }
    event?.chipInput?.clear();
    this.touchControls();
  }

  removeTag(tagIndex: number): void {
    if (tagIndex >= 0) {
      const tags = [...this.chosenTagsControl.value];
      tags.splice(tagIndex, 1);
      this.value = tags;
      this.onChange(this.value);
    }
    this.touchControls();
  }

  selectExistingTagFromAutocomplete(event: MatAutocompleteSelectedEvent): void {
    if (this.searchInput) this.searchInput.nativeElement.value = '';
    this.searchControl.setValue('');

    const tag = event?.option?.viewValue;
    if (tag) {
      this.addTag(tag);
    }
    this.touchControls();
  }

  /**
   * Implement MatFormFieldControl interface
   */
  @Input({ transform: booleanAttribute })
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this.stateChanges.next();
  }
  protected _disabled = false;

  @Input({ transform: booleanAttribute })
  get required(): boolean {
    return this._required;
  }
  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges.next();
  }
  protected _required = false;

  @Input()
  get value(): string[] | null {
    return this.chosenTagsControl.value;
  }
  set value(tags: string[] | null) {
    this.chosenTagsControl.setValue(tags ?? []);
    this.onChange(tags);
    this.stateChanges.next();
  }

  @Input()
  get placeholder() {
    return this._placeholder;
  }
  set placeholder(plh) {
    this._placeholder = plh;
    this.stateChanges.next();
  }
  protected _placeholder = 'GALAXY.TAGS.PLACEHOLDER';

  get empty(): boolean {
    return this.value?.length === 0;
  }

  get errorState(): boolean {
    return this.ngControl.errors !== null && !!this.ngControl.touched;
  }

  @HostBinding('[id]') id = `tags-control-${TagsComponent.nextId++}`;
  @HostBinding('[class.floating]') get shouldLabelFloat(): boolean {
    return false;
  }

  readonly stateChanges = new Subject<void>();
  focused = false;
  readonly controlType = 'tags-input';
  autofilled?: boolean;
  userAriaDescribedBy?: string;
  onChange: (value: string[] | null) => void = () => ({});
  onTouched: () => void = () => ({});

  setDescribedByIds(ids: string[]): void {
    this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));
  }
  onContainerClick(): void {
    this._elementRef.nativeElement.focus();
  }

  /**
   * Implement ControlValueAccessor interface
   */
  writeValue(value: string[]): void {
    this.value = value;
  }
  registerOnChange(fn: (v: string[] | null) => void): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
