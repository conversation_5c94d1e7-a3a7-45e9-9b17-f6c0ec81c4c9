<ng-container>
  <mat-chip-grid #chipGrid [required]="_required" [disabled]="_disabled">
    @for (tag of chosenTagsControl.value; track tag; let tagIndex = $index) {
      <mat-chip-row (removed)="removeTag(tagIndex)">
        {{ tag || '-' }}
        <mat-icon matChipRemove>cancel</mat-icon>
      </mat-chip-row>
    }
    <input
      #searchInput
      [hidden]="maxTags !== null && chosenTagsControl.value.length >= maxTags"
      [placeholder]="_placeholder | translate"
      maxlength="{{ maxTagLength }}"
      [formControl]="searchControl"
      [matAutocomplete]="auto"
      [matChipInputFor]="chipGrid"
      [matChipInputAddOnBlur]="addOnBlur"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      (matChipInputTokenEnd)="addNewTagByTokenEnd($event)"
    />
  </mat-chip-grid>
  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selectExistingTagFromAutocomplete($event)">
    @for (tag of availableTags; track tag) {
      <mat-option [value]="tag">{{ tag }}</mat-option>
    }
  </mat-autocomplete>
</ng-container>
