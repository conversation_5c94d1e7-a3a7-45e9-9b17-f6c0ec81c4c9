import { Inject, Injectable } from '@angular/core';
import { AUTOMATION_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/automata/shared';
import { ListFieldSchemasResponse, ObjectType, SchemaService } from '@vendasta/auxiliary-data-components';
import { combineLatest, concatMap, Observable, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { AuxiliaryDataFilterGeneratorService, FieldSchema, FieldType } from './auxiliary-data-filter-generator.service';
import { customAccountDataKey, customProductDataKey, customSalesOrderDataKey, customUserDataKey } from './data-keys';
import { CRMFieldSchemaApiService, FieldSchema as CRMFieldSchema } from '@vendasta/crm';
import { DataEntry, DataFilter } from '../interface';
import { AuxiliaryDataFieldSchemaInterface } from '@vendasta/account-group/lib/_internal/interfaces/fields.interface';

const FIELD_SCHEMA_PAGE_SIZE = 100;

function auxiliaryFieldSchemaToFieldSchema(fieldSchema: AuxiliaryDataFieldSchemaInterface): FieldSchema {
  return {
    partnerId: fieldSchema.partnerId || '',
    fieldId: fieldSchema.fieldId || '',
    fieldName: fieldSchema.fieldName || '',
    fieldDescription: fieldSchema.fieldDescription || '',
    fieldType: fieldSchema.fieldType as unknown as FieldType,
    staticDropdownOptions: (fieldSchema.dropdownOptions || []).map((option) => ({
      value: option?.value || '',
      label: option?.label || '',
    })),
    currencyCode: fieldSchema.currencyCode,
    archived: fieldSchema.archived,
  };
}

@Injectable({ providedIn: 'root' })
export class AuxiliaryDataFilterService {
  constructor(
    @Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) private readonly namespace$: Observable<string>,
    private readonly schemaService: SchemaService,
    private readonly auxiliaryDataFilterGenerator: AuxiliaryDataFilterGeneratorService,
    private readonly crmFieldSchemaApiService: CRMFieldSchemaApiService,
  ) {}

  // custom_account_data
  // PageSize is limited to 100, if the number of auxiliary data grows,
  // this has to be revisited to think how to properly display
  // filters in the dropdown.
  private availableAccountGroupSchemas$ = this.getAvailableFieldSchemas$((partnerId) =>
    this.schemaService.listFieldSchemas('business', partnerId, false, '', FIELD_SCHEMA_PAGE_SIZE),
  ).pipe(shareReplay({ bufferSize: 1, refCount: true }));
  private archivedAccountGroupSchemas$ = this.getArchivedFieldSchemasInAutomation$('business').pipe(
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private accountGroupAllSchemas$ = this.combineFieldSchemas$(
    this.availableAccountGroupSchemas$,
    this.archivedAccountGroupSchemas$,
  );
  public customAccountGroupDataFilters$ = this.auxiliaryDataFilterGenerator.generateDataFilters(
    customAccountDataKey,
    this.accountGroupAllSchemas$,
  );

  // custom_user_data
  private availableUserSchemas$ = this.getAvailableFieldSchemas$((partnerId) =>
    this.schemaService.listFieldSchemas('user', partnerId, false, '', FIELD_SCHEMA_PAGE_SIZE),
  ).pipe(shareReplay({ bufferSize: 1, refCount: true }));
  private archivedUserSchemas$ = this.getArchivedFieldSchemasInAutomation$('user').pipe(
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private userAllSchemas$ = this.combineFieldSchemas$(this.availableUserSchemas$, this.archivedUserSchemas$);
  public customUserDataFilters$ = this.auxiliaryDataFilterGenerator.generateDataFilters(
    customUserDataKey,
    this.userAllSchemas$,
  );

  // custom_sales_order_data
  private availableOrderSchemas$ = this.getAvailableFieldSchemas$((partnerId) =>
    this.schemaService.listFieldSchemas('order', partnerId, false, '', FIELD_SCHEMA_PAGE_SIZE),
  ).pipe(shareReplay({ bufferSize: 1, refCount: true }));
  private archivedOrderSchemas$ = this.getArchivedFieldSchemasInAutomation$('order').pipe(
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private orderAllSchemas$ = this.combineFieldSchemas$(this.availableOrderSchemas$, this.archivedOrderSchemas$);
  public customOrderDataFilters$ = this.auxiliaryDataFilterGenerator.generateDataFilters(
    customSalesOrderDataKey,
    this.orderAllSchemas$,
  );

  // custom_product_data
  private availableProductSchemas$ = this.getAvailableFieldSchemas$((partnerId) =>
    this.schemaService.listFieldSchemas('product', partnerId, false, '', FIELD_SCHEMA_PAGE_SIZE),
  ).pipe(shareReplay({ bufferSize: 1, refCount: true }));
  private archivedProductSchemas$ = this.getArchivedFieldSchemasInAutomation$('product').pipe(
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private productAllSchemas$ = this.combineFieldSchemas$(this.availableProductSchemas$, this.archivedProductSchemas$);
  public customProductDataFilters$ = this.auxiliaryDataFilterGenerator.generateDataFilters(
    customProductDataKey,
    this.productAllSchemas$,
  );

  public getCRMObjectDataFilters(
    dataKey: string,
    crmObjectType: string,
    crmObjectSubType?: string,
  ): Observable<Map<string, DataFilter>> {
    const fields$ = this.namespace$.pipe(
      switchMap((partnerId) => {
        return this.crmFieldSchemaApiService.listFieldSchema({
          namespace: partnerId,
          crmObjectType: crmObjectType,
          crmObjectSubtype: crmObjectSubType,
          pagingOptions: {
            cursor: '',
            pageSize: 200,
          },
        });
      }),
      map((resp) => this.convertCRMSchemaToAuxSchema(crmObjectType, resp?.fieldSchemas)),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
    return this.auxiliaryDataFilterGenerator.generateDataFilters(dataKey, fields$);
  }

  public getCRMObjectDataEntryComponents(
    dataKey: string,
    crmObjectType: string,
    crmObjectSubType?: string,
    includeReadonlyFields = true,
  ): Observable<DataEntry[]> {
    const fields$ = this.namespace$.pipe(
      switchMap((partnerId) => {
        return this.crmFieldSchemaApiService.listFieldSchema({
          namespace: partnerId,
          crmObjectType: crmObjectType,
          crmObjectSubtype: crmObjectSubType,
          pagingOptions: {
            cursor: '',
            pageSize: 200,
          },
        });
      }),
      map((resp) => {
        if (includeReadonlyFields) {
          return resp?.fieldSchemas || [];
        }
        return (resp?.fieldSchemas || []).filter((fieldSchema: CRMFieldSchema) => !fieldSchema.readonly);
      }),
      map((resp) => this.convertCRMSchemaToAuxSchema(crmObjectType, resp)),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
    return this.auxiliaryDataFilterGenerator.generateDataEntryComponents(dataKey, fields$);
  }

  private getAvailableFieldSchemas$(
    listFieldSchema: (value: string, index: number) => Observable<ListFieldSchemasResponse>,
  ): Observable<FieldSchema[]> {
    return this.namespace$.pipe(
      switchMap(listFieldSchema),
      catchError((_) => []),
      take(1),
      map((response) => {
        return (response.fieldSchemas || []).map((fieldSchema) => auxiliaryFieldSchemaToFieldSchema(fieldSchema));
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  private getArchivedFieldSchemasInAutomation$(objectType: ObjectType): Observable<FieldSchema[]> {
    return this.namespace$.pipe(
      take(1), // WARNING: This take(1) is required, otherwise the forkJoin (waaaaaaaaay down the chain) will not emit
      switchMap((namespace) => this.loadAllArchivedFieldSchemas(namespace, objectType, '', [])),
    );
  }

  private loadAllArchivedFieldSchemas(
    namespace: string,
    objectType: ObjectType,
    cursor: string,
    schemas: FieldSchema[],
  ): Observable<FieldSchema[]> {
    return this.schemaService.listFieldSchemas(objectType, namespace, true, cursor, FIELD_SCHEMA_PAGE_SIZE).pipe(
      concatMap((response) => {
        (response?.fieldSchemas || []).map((fs) => {
          schemas.push(auxiliaryFieldSchemaToFieldSchema(fs));
        });

        if (response?.pagingMetadata?.hasMore) {
          return this.loadAllArchivedFieldSchemas(namespace, objectType, response?.pagingMetadata?.nextCursor, schemas);
        } else {
          return of(schemas);
        }
      }),
      catchError(() => {
        return of([]);
      }),
    );
  }

  private combineFieldSchemas$(
    availableSchemas$: Observable<FieldSchema[]>,
    archivedSchemas$: Observable<FieldSchema[]>,
  ): Observable<FieldSchema[]> {
    return combineLatest([availableSchemas$, archivedSchemas$]).pipe(
      map(([unarchivedFieldSchemas, archivedFieldSchemas]) => {
        return [...unarchivedFieldSchemas, ...archivedFieldSchemas];
      }),
    );
  }

  private convertCRMSchemaToAuxSchema(crmObjectType: string, schemas: CRMFieldSchema[]): FieldSchema[] {
    const fieldSchemas: FieldSchema[] = [];

    for (const fieldSchema of schemas) {
      const fieldType = fieldSchema.fieldType as FieldType;
      fieldSchemas.push({
        // The namespace isn't always the partner ID so this might not always work, seems good enough for now
        partnerId: fieldSchema.namespace,
        fieldId: fieldSchema.fieldId,
        fieldName: fieldSchema.fieldName,
        fieldDescription: fieldSchema.fieldDescription,
        fieldType: fieldType,
        currencyCode: fieldSchema.currencyCode,
        // These are on the CRM field schema but not the auxiliary data field schema
        // externalId: fieldSchema.externalId,
        // created: fieldSchema.created,
        // updated: fieldSchema.updated,
        archived: fieldSchema.archived,
        fieldConfig: {
          crmObjectType: crmObjectType,
          crmObjectSubtype: fieldSchema.crmObjectSubtype,
        },
      });
    }

    return fieldSchemas;
  }
}
