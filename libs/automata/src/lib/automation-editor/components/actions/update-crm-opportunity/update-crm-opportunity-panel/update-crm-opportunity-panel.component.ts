import { Component, OnInit, signal, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
} from '@angular/forms';
import { RuleInterface } from '@vendasta/automata';
import { StandardIds } from '@galaxy/crm/static';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { AuxiliaryDataFilterService } from '../../../filters';
import { DataEntryComponent, DataEntryReturn } from '../../create-company/data-entry/data-entry.component';
import { customOpportunityDataKey } from '../../../filters/definitions/auxiliary-data-filter/data-keys';
import { MatDialog } from '@angular/material/dialog';
import { CrmFieldSelectorDialogComponent } from '../../create-company/crm-field-selector-dialog/crm-field-selector-dialog.component';
import { take } from 'rxjs/operators';
import {
  FIRST_STAGE,
  PipelineSelectorComponent,
} from '../../../filters/definitions/auxiliary-data-filter/custom-filters/opportunity-pipeline-stage/pipeline-selector/pipeline-selector.component';

const OBJECT_TYPE_OPPORTUNITY = 'Opportunity';
const OPPORTUNITY_PIPELINE_FIELD_IDS = [StandardIds.OpportunityPipelineID, StandardIds.OpportunityCalculatedStageID];

@Component({
  selector: 'automata-update-crm-opportunity-panel',
  templateUrl: './update-crm-opportunity-panel.component.html',
  styleUrls: ['./update-crm-opportunity-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    DataEntryComponent,
    PipelineSelectorComponent,
  ],
})
export class UpdateCrmOpportunityPanelComponent extends CommonActionPanelComponent implements OnInit {
  components$ = this.auxService.getCRMObjectDataEntryComponents(
    customOpportunityDataKey,
    OBJECT_TYPE_OPPORTUNITY,
    '',
    false,
  );
  formArray = new FormArray<FormControl<DataEntryReturn>>([], [fieldIdsValidator]);

  @ViewChild('crmPipelineSelector', { static: false }) pipelineSelector: PipelineSelectorComponent | undefined;
  protected initialPipelineId = '';
  protected initialStageId = '';
  showPipelineSelector = signal<boolean>(false);
  protected readonly FIRST_STAGE = FIRST_STAGE;

  constructor(
    private readonly auxService: AuxiliaryDataFilterService,
    private dialog: MatDialog,
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    const jsonData = this?.step?.data ? JSON.parse(this.step.data) : { fields: [] };
    this.initializeFormValues(Object.entries(jsonData.fields).map(([key, value]) => ({ fieldId: key, value: value })));
    this.savingEnabled = true;
  }

  getRules(): RuleInterface[] {
    return [];
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step?.data ?? '') : {};
    const fields = {};
    this.formArray.controls
      .filter((c) => c?.value?.fieldId && (c?.value?.value !== null || c?.value?.value !== undefined))
      .forEach((control: AbstractControl) => (fields[control.value.fieldId] = control.value.value));
    if (this.showPipelineSelector() && this.pipelineSelector) {
      const [pipelineId, stageId] = this.pipelineSelector.getSelectedValue();
      fields[StandardIds.OpportunityPipelineID] = pipelineId;
      fields[StandardIds.OpportunityCalculatedStageID] = stageId;
    }
    jsonData.fields = fields;
    return jsonData;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.formArray];
  }

  isFormValid(): boolean | string {
    const pipelineInvalid = this.showPipelineSelector() && !this.pipelineSelector?.isFormValid();
    if (pipelineInvalid || !this.formArray.valid) {
      return 'AUTOMATIONS.EDITOR.TASKS.UPDATE_OPPORTUNITY.EMPTY_FIELD_MESSAGE';
    }
    return true;
  }

  addDataEntryComponent(): void {
    this.dialog
      .open(CrmFieldSelectorDialogComponent, {
        data: {
          crmObjectType: OBJECT_TYPE_OPPORTUNITY,
          fieldOverrides: [
            {
              fieldId: StandardIds.OpportunityPipelineID,
              fieldNameKey: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_OPPORTUNITY.FIELD_OVERRIDES.PIPELINE_AND_STAGE',
            },
            {
              fieldId: StandardIds.OpportunityCalculatedStageID,
              hidden: true,
            },
            {
              fieldId: StandardIds.OpportunityProbability,
              hidden: true,
            },
          ],
        },
        width: '400px',
      })
      .afterClosed()
      .pipe(take(1))
      .subscribe((fieldIds) => {
        fieldIds = (fieldIds || []).filter(
          (fieldId: string) => !this.formArray.controls.map((c) => c?.value?.fieldId).includes(fieldId),
        );
        this.initializeFormValues(fieldIds.map((fieldId: string) => ({ fieldId, value: '' })));
        this.cdr.detectChanges();
      });
  }

  removeDataEntryComponent(index: number): void {
    if (OPPORTUNITY_PIPELINE_FIELD_IDS.includes(this.formArray.at(index).value.fieldId)) {
      this.removePipelineFields();
      return;
    }
    this.formArray.removeAt(index);
  }

  removePipelineFields(): void {
    this.showPipelineSelector.set(false);
    this.initialPipelineId = '';
    this.initialStageId = '';
  }

  initializeFormValues(fields: { fieldId: string; value: unknown }[]): void {
    let pipelineId = '';
    let stageId = '';
    let hasPipelineField = false;
    for (const field of fields) {
      if (OPPORTUNITY_PIPELINE_FIELD_IDS.includes(field.fieldId)) {
        hasPipelineField = true;
        if (field.fieldId === StandardIds.OpportunityPipelineID) {
          pipelineId = String(field?.value || '');
        }
        if (field.fieldId === StandardIds.OpportunityCalculatedStageID) {
          stageId = String(field?.value || '');
        }
        continue;
      }
      this.formArray.push(new FormControl<DataEntryReturn>(field));
    }
    if (hasPipelineField) {
      this.initialPipelineId = pipelineId;
      this.initialStageId = stageId;
      this.showPipelineSelector.set(true);
    }
  }

  fieldIsOverridden(fieldId: string): boolean {
    return OPPORTUNITY_PIPELINE_FIELD_IDS.includes(fieldId);
  }
}

function fieldIdsValidator(control: AbstractControl): ValidationErrors | null {
  const formArray = control as FormArray;
  const fieldIds = formArray.controls.filter((c) => !!c.value).map((c) => c.value.fieldId);
  const uniqueFieldIds = new Set(fieldIds);
  if (fieldIds.length !== uniqueFieldIds.size) {
    return { duplicateFieldIds: true };
  }
  if (fieldIds.filter((id) => !id).length > 0) {
    return { emptyFieldId: true };
  }
  return null;
}
