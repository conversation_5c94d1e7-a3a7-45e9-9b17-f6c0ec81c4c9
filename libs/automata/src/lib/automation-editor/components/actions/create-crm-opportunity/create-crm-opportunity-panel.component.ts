import { Component, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
} from '@angular/forms';
import { RuleInterface } from '@vendasta/automata';
import { CommonActionPanelComponent } from '../../common/common-action-panel-component.directive';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { AuxiliaryDataFilterService } from '../../filters';
import { DataEntryComponent, DataEntryReturn } from '../create-company/data-entry/data-entry.component';
import { customOpportunityDataKey } from '../../filters/definitions/auxiliary-data-filter/data-keys';
import { MatDialog } from '@angular/material/dialog';
import { CrmFieldSelectorDialogComponent } from '../create-company/crm-field-selector-dialog/crm-field-selector-dialog.component';
import { map, take } from 'rxjs/operators';
import { CrmFieldService, StandardIds } from '@galaxy/crm/static';
import {
  FIRST_STAGE,
  PipelineSelectorComponent,
} from '../../filters/definitions/auxiliary-data-filter/custom-filters/opportunity-pipeline-stage/pipeline-selector/pipeline-selector.component';
import { Observable, of } from 'rxjs';

@Component({
  selector: 'automata-create-opportunity-panel',
  templateUrl: './create-crm-opportunity-panel.component.html',
  styleUrls: ['./create-crm-opportunity-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    DataEntryComponent,
    PipelineSelectorComponent,
  ],
})
export class CreateCrmOpportunityPanelComponent extends CommonActionPanelComponent implements OnInit {
  @ViewChild(PipelineSelectorComponent) pipelineSelector!: PipelineSelectorComponent;
  components$ = this.auxService
    .getCRMObjectDataEntryComponents(customOpportunityDataKey, 'Opportunity', '', false)
    .pipe(
      map((components) => components.filter((component) => component.fieldId !== StandardIds.OpportunityPipelineID)),
      map((components) =>
        components.filter((component) => component.fieldId !== StandardIds.OpportunityCalculatedStageID),
      ),
    );
  formArray = new FormArray<FormControl<DataEntryReturn>>([], [fieldIdsValidator]);
  requiredFieldIds: string[] = [];

  protected pipelineSelectorInitialValues$: Observable<[string, string] | []> | undefined;

  protected readonly FIRST_STAGE = FIRST_STAGE;

  constructor(
    private readonly auxService: AuxiliaryDataFilterService,
    private dialog: MatDialog,
    private crmFieldService: CrmFieldService,
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.requiredFieldIds = [this.crmFieldService.getFieldId(StandardIds.OpportunityName)];

    const jsonData = this.step ? JSON.parse(this.step.data) : { fields: [] };

    const pipelineId = jsonData?.fields?.[StandardIds.OpportunityPipelineID];
    const stageId = jsonData?.fields?.[StandardIds.OpportunityCalculatedStageID];

    this.pipelineSelectorInitialValues$ = of([pipelineId, stageId]);

    for (const [key, value] of Object.entries(jsonData?.fields)) {
      if (key === StandardIds.OpportunityPipelineID || key === StandardIds.OpportunityCalculatedStageID) {
        continue;
      }
      this.formArray.push(new FormControl<DataEntryReturn>({ fieldId: key, value: value }));
    }
    if (Object.entries(jsonData?.fields).length === 0) {
      this.requiredFieldIds.forEach((fieldId) => {
        this.formArray.push(new FormControl<DataEntryReturn>({ fieldId: fieldId, value: '' }));
      });
    }

    this.savingEnabled = true;
  }

  getRules(): RuleInterface[] {
    return [];
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step?.data ?? '') : {};
    const fields = {};
    this.formArray.controls
      .filter((c) => c?.value?.fieldId && (c?.value?.value !== null || c?.value?.value !== undefined))
      .forEach((control: AbstractControl) => (fields[control.value.fieldId] = control.value.value));

    const [pipelineId, stageId] = this.pipelineSelector.getSelectedValue();
    fields[StandardIds.OpportunityPipelineID] = pipelineId;
    fields[StandardIds.OpportunityCalculatedStageID] = stageId;

    jsonData.fields = fields;
    return jsonData;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.formArray, this.pipelineSelector.pipelineControl];
  }

  isFormValid(): boolean | string {
    const oneOfRequiredFieldsHasAValue = this.formArray.controls
      .filter((c) => this.requiredFieldIds.includes(c.value.fieldId))
      .reduce(
        (acc, control) =>
          acc || (control.value.value !== null && control.value.value !== undefined && control.value.value !== ''),
        false,
      );
    if (!oneOfRequiredFieldsHasAValue) {
      return 'AUTOMATIONS.EDITOR.TASKS.CREATE_CRM_OPPORTUNITY.REQUIRED_MESSAGE';
    }
    return this.formArray.valid && this.pipelineSelector.isFormValid();
  }

  addDataEntryComponent(): void {
    this.dialog
      .open(CrmFieldSelectorDialogComponent, {
        data: {
          crmObjectType: 'Opportunity',
          fieldOverrides: [
            {
              fieldId: StandardIds.OpportunityPipelineID,
              hidden: true,
            },
            {
              fieldId: StandardIds.OpportunityCalculatedStageID,
              hidden: true,
            },
            {
              fieldId: StandardIds.OpportunityProbability,
              hidden: true,
            },
          ],
        },
        width: '400px',
      })
      .afterClosed()
      .pipe(take(1))
      .subscribe((fieldIds) => {
        (fieldIds || [])
          .filter((fieldId) => !this.formArray.controls.map((c) => c?.value?.fieldId).includes(fieldId))
          .forEach((fieldId: string) => {
            this.formArray.push(new FormControl({ fieldId: fieldId, value: '' }));
          });
        this.cdr.detectChanges();
      });
  }

  removeDataEntryComponent(index: number): void {
    this.formArray.removeAt(index);
  }
}

function fieldIdsValidator(control: AbstractControl): ValidationErrors | null {
  const formArray = control as FormArray;
  const fieldIds = formArray.controls.filter((c) => !!c.value).map((c) => c.value.fieldId);
  const uniqueFieldIds = new Set(fieldIds);
  if (fieldIds.length !== uniqueFieldIds.size) {
    console.error('duplicate field ids found');
    return { duplicateFieldIds: true };
  }
  if (fieldIds.filter((id) => !id).length > 0) {
    console.error('empty field id found');
    return { emptyFieldId: true };
  }
  return null;
}
