import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
} from '@angular/forms';
import { RuleInterface } from '@vendasta/automata';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { AuxiliaryDataFilterService } from '../../../filters';
import { DataEntryComponent, DataEntryReturn } from '../../create-company/data-entry/data-entry.component';
import { customContactDataKey } from '../../../filters/definitions/auxiliary-data-filter/data-keys';
import { MatDialog } from '@angular/material/dialog';
import { CrmFieldSelectorDialogComponent } from '../../create-company/crm-field-selector-dialog/crm-field-selector-dialog.component';
import { take } from 'rxjs/operators';

@Component({
  selector: 'automata-update-contact-panel',
  templateUrl: './update-contact-panel.component.html',
  styleUrls: ['./update-contact-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    DataEntryComponent,
  ],
})
export class UpdateContactPanelComponent extends CommonActionPanelComponent implements OnInit {
  contact = 'Contact';
  components$ = this.auxService.getCRMObjectDataEntryComponents(customContactDataKey, this.contact, '', false);
  formArray = new FormArray<FormControl<DataEntryReturn>>([], [fieldIdsValidator]);

  constructor(
    private readonly auxService: AuxiliaryDataFilterService,
    private dialog: MatDialog,
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    const jsonData = this.step ? JSON.parse(this.step.data) : { fields: [] };
    for (const [key, value] of Object.entries(jsonData?.fields)) {
      this.formArray.push(new FormControl<DataEntryReturn>({ fieldId: key, value: value }));
    }
    this.savingEnabled = true;
  }

  getRules(): RuleInterface[] {
    return [];
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step?.data ?? '') : {};
    const fields = {};
    this.formArray.controls
      .filter((c) => c?.value?.fieldId && (c?.value?.value !== null || c?.value?.value !== undefined))
      .forEach((control: AbstractControl) => (fields[control.value.fieldId] = control.value.value));
    jsonData.fields = fields;
    return jsonData;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.formArray];
  }

  isFormValid(): boolean | string {
    if (!this.formArray.valid) {
      return 'AUTOMATIONS.EDITOR.TASKS.UPDATE_CONTACT.EMPTY_FIELD_MESSAGE';
    }
    return this.formArray.valid;
  }

  addDataEntryComponent(): void {
    this.dialog
      .open(CrmFieldSelectorDialogComponent, {
        data: {
          crmObjectType: this.contact,
        },
        width: '400px',
      })
      .afterClosed()
      .pipe(take(1))
      .subscribe((fieldIds) => {
        (fieldIds || [])
          .filter((fieldId) => !this.formArray.controls.map((c) => c?.value?.fieldId).includes(fieldId))
          .forEach((fieldId: string) => {
            this.formArray.push(new FormControl({ fieldId: fieldId, value: '' }));
          });
        this.cdr.detectChanges();
      });
  }

  removeDataEntryComponent(index: number): void {
    this.formArray.removeAt(index);
  }
}

function fieldIdsValidator(control: AbstractControl): ValidationErrors | null {
  const formArray = control as FormArray;
  const fieldIds = formArray.controls.filter((c) => !!c.value).map((c) => c.value.fieldId);
  const uniqueFieldIds = new Set(fieldIds);
  if (fieldIds.length !== uniqueFieldIds.size) {
    return { duplicateFieldIds: true };
  }
  if (fieldIds.filter((id) => !id).length > 0) {
    return { emptyFieldId: true };
  }
  return null;
}
