import { CommonModule } from '@angular/common';
import { Component, DestroyRef, inject, Inject, Input, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { AUTOMATION_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/automata/shared';
import { TranslateModule } from '@ngx-translate/core';
import { RuleInterface } from '@vendasta/automata';
import { AuxiliaryDataModule, FieldHandler, ObjectType, RendererSelector } from '@vendasta/auxiliary-data-components';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { combineLatest, Observable, of } from 'rxjs';
import { distinctUntilChanged, filter, map, shareReplay, startWith, switchMap, take, tap } from 'rxjs/operators';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { CRMFieldSchemaApiService, FieldSchema } from '@vendasta/crm';
import { CRMFieldOperations } from '../index';
import { chooseFieldHandler } from './utilities';
import { OperationFilterPipe } from '../operation-filter.pipe';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

interface ModifyCRMDataForm {
  selectedField: FormControl<FieldSchema | undefined>;
  selectedOperation: FormControl<string | null>;
}

@Component({
  selector: 'automata-modify-crm-data-panel',
  templateUrl: './modify-crm-data-panel.component.html',
  styleUrls: ['./modify-crm-data-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatDialogModule,
    GalaxyWrapModule,
    AuxiliaryDataModule,
    OperationFilterPipe,
    OperationFilterPipe,
  ],
  providers: [CRMFieldSchemaApiService],
  standalone: true,
})
export class ModifyCRMDataPanelComponent extends CommonActionPanelComponent implements OnInit {
  @Input() objectType!: ObjectType;
  @Input() panelTitle!: string;
  @Input() panelSubtitle!: string;

  CustomFieldOperations = CRMFieldOperations;

  form!: FormGroup<ModifyCRMDataForm>;
  fieldSearch = new FormControl<string | FieldSchema>('');
  selectedField = new FormControl<FieldSchema | undefined>(undefined, [Validators.required]);
  selectedOperation = new FormControl<string | null>('', [Validators.required]);
  @ViewChild('valueField', { static: false }) valueField!: RendererSelector;

  fieldSchemas$!: Observable<FieldSchema[]>;
  filteredFieldSchemas$!: Observable<FieldSchema[]>;
  selectedFieldSchema$!: Observable<FieldSchema>;
  selectedFieldHandler$!: Observable<FieldHandler>;

  private firstLoad = true;

  destroyRef = inject(DestroyRef);

  constructor(
    @Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) private readonly namespace$: Observable<string>,
    private readonly crmFieldSchemaApiService: CRMFieldSchemaApiService,
    protected readonly fb: FormBuilder,
  ) {
    super();
  }

  private isArchivedFieldSchema(fieldSchemas: FieldSchema[], archivedFieldSchema: FieldSchema): boolean {
    if (!archivedFieldSchema) {
      return false;
    }
    if (fieldSchemas.length === 0) {
      return true;
    }
    return fieldSchemas.findIndex((fieldSchema) => fieldSchema?.fieldId === archivedFieldSchema?.fieldId) === -1;
  }

  private setFieldSchemasObservables(fieldId: string, partnerId: string): void {
    const archivedFieldSchema$ = of(!!fieldId).pipe(
      switchMap((hasFieldId) => {
        if (!hasFieldId) {
          return of(null);
        }
        return this.crmFieldSchemaApiService
          .getMultiFieldSchema({
            namespace: partnerId,
            crmObjectType: this.objectType,
            fieldId: [fieldId],
          })
          .pipe(map((response) => response.fieldSchemas[0]));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    const availableSchemas$ = this.namespace$.pipe(
      switchMap((partnerId) =>
        this.crmFieldSchemaApiService.listFieldSchema({
          namespace: partnerId,
          crmObjectType: this.objectType,
          pagingOptions: { pageSize: 100 },
        }),
      ),
      map((response) => response?.fieldSchemas || []),
      map((schemas) => schemas.filter((schema) => !schema.readonly)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.fieldSchemas$ = combineLatest([availableSchemas$, archivedFieldSchema$]).pipe(
      switchMap(([fieldSchemas, archivedFieldSchema]) => {
        if (this.isArchivedFieldSchema(fieldSchemas, archivedFieldSchema!)) {
          return of([...fieldSchemas, archivedFieldSchema]);
        }
        return of(fieldSchemas);
      }),
    );
    this.filteredFieldSchemas$ = combineLatest([
      this.fieldSchemas$,
      this.fieldSearch.valueChanges.pipe(startWith('')),
    ]).pipe(
      map(([fieldSchemas, fieldSearch]) => {
        if (!fieldSearch || typeof fieldSearch !== 'string') {
          return fieldSchemas;
        }
        return fieldSchemas.filter((schema) => schema.fieldName.toLowerCase().includes(fieldSearch.toLowerCase()));
      }),
    );
  }

  private setSelectedFieldObservables(jsonData: any): void {
    this.selectedField.updateValueAndValidity();
    this.selectedField.valueChanges
      .pipe(
        startWith(jsonData?.field_id ? { fieldId: jsonData?.field_id } : null),
        map((fs) => fs?.fieldId),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        if (!this.firstLoad) {
          this.selectedOperation.setValue(null);
        }
      });
    const selectedFieldChanges$ = this.selectedField.valueChanges.pipe(
      startWith(jsonData?.field_id ? { fieldId: jsonData?.field_id } : null),
      map((fs) => fs?.fieldId),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.selectedFieldSchema$ = combineLatest([this.fieldSchemas$, selectedFieldChanges$]).pipe(
      map(([fieldSchemas, selectedField]) =>
        fieldSchemas?.find((fieldSchema) => fieldSchema.fieldId === selectedField),
      ),
      distinctUntilChanged((prev, curr) => prev?.fieldId === curr?.fieldId),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    const selectedOperationChanges$ = this.selectedOperation.valueChanges.pipe(
      startWith(jsonData?.operation || ''),
      distinctUntilChanged(),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.selectedFieldHandler$ = combineLatest([this.selectedFieldSchema$, selectedOperationChanges$]).pipe(
      filter(() => !this.isProcessing),
      map(([selectedFieldSchema, selectedOperation]) => {
        let fieldValue: any = jsonData?.value || null;
        if (!this.firstLoad) {
          fieldValue = null;
        }
        return chooseFieldHandler(selectedFieldSchema, selectedOperation, fieldValue);
      }),
      tap(() => (this.firstLoad = false)),
    );
  }

  private addAsyncValidatorToFieldSelector(): void {
    this.selectedField.setAsyncValidators((control) => {
      return this.selectedFieldSchema$.pipe(
        take(1),
        switchMap((selectedFieldSchema) => {
          if (!!selectedFieldSchema?.archived && control.value?.fieldId === selectedFieldSchema.fieldId) {
            return of({ archivedField: true });
          }
          return of(null);
        }),
        tap((error) => {
          if (error?.archivedField) {
            this.selectedField.markAsTouched();
          }
        }),
      );
    });
    this.selectedField.updateValueAndValidity();
  }

  ngOnInit(): void {
    super.ngOnInit();

    // Here's an example of the jsonData that comes into these CRM data components:
    // {
    //     "object_type": "Contact",
    //     "field_id": "FieldID-a7b68b66-6820-4a44-9717-f8f4e1d399ce",
    //     "operation": "SET_VALUE",
    //     "value": [
    //         "#hi"
    //     ]
    // }
    const jsonData = this.step ? JSON.parse(this.step.data!) : null;

    this.form = this.fb.group<ModifyCRMDataForm>({
      selectedField: this.selectedField,
      selectedOperation: this.selectedOperation,
    });
    this.namespace$
      .pipe(take(1))
      .subscribe((partnerId) => this.setFieldSchemasObservables(jsonData?.field_id, partnerId));
    this.setSelectedFieldObservables(jsonData);
    this.addAsyncValidatorToFieldSelector();

    this.fieldSchemas$.pipe(take(1)).subscribe((schemas) => {
      if (jsonData) {
        const foundSchema = schemas?.find((fieldSchema) => fieldSchema.fieldId === jsonData.field_id);
        this.selectedField.setValue(foundSchema!);
        this.fieldSearch.setValue(foundSchema!);
        this.selectedOperation.setValue(jsonData.operation);
      }
    });
    this.savingEnabled = true;
  }

  onFieldSelected(field: MatAutocompleteSelectedEvent): void {
    if (!field?.option?.value) {
      return;
    }
    this.selectedField.setValue(field.option.value);
    this.selectedField.updateValueAndValidity();
  }

  getControlsToValidateOnSave(): (FormControl | FormGroup)[] {
    const controlsToValidate: (FormControl | FormGroup)[] = [this.form];

    if (this.valueField?.handler?.formControl) {
      controlsToValidate.push(this.valueField.handler.formControl);
    }

    return controlsToValidate;
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step.data!) : {};
    if (!this.form) {
      return jsonData;
    }

    jsonData.object_type = this.objectType;
    jsonData.field_id = this.selectedField.value?.fieldId;
    jsonData.operation = this.selectedOperation.value;
    if (jsonData.operation !== 'CLEAR_VALUE') {
      jsonData.value = this.valueField?.handler?.formControl?.value;
    } else {
      jsonData.value = null;
    }

    return jsonData;
  }

  getRules(): RuleInterface[] {
    return [];
  }

  isFormValid(): boolean {
    if (this.valueField?.handler?.formControl) {
      return this.form.valid && this.valueField.handler.formControl.valid;
    }

    return this.form.valid;
  }

  fieldName(field: FieldSchema): string {
    return field?.fieldName;
  }
}
