import { Component, inject, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Citation, NamespaceType } from '../citations';
import { FunctionApiService, FunctionParameterParameterLocation, NamespaceInterface } from '@vendasta/ai-assistants';
import { firstValueFrom } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Clipboard } from '@angular/cdk/clipboard';
import { AlertComponent } from '@vendasta/galaxy/alert/src/alert.component';

@Component({
  selector: 'inbox-function-call-modal',
  templateUrl: './function-call-modal.component.html',
  styleUrl: './function-call-modal.component.scss',
  imports: [MatDialogModule, TranslateModule, CommonModule, MatButtonModule, MatIconModule, AlertComponent],
})
export class FunctionCallModalComponent {
  showFullRequest = false;
  functionCompleteURL = '';
  functionRequestBodyArgs: any = null;
  functionURLQueryArgs: any = null;
  functionHeaders: any = null;
  content: any = null;
  contentLimitExceeded = false;
  contentMaxKilobytes = 0;
  private functionData: any = null;

  private readonly functionApiService = inject(FunctionApiService);
  private readonly clipboard = inject(Clipboard);

  constructor(
    public dialogRef: MatDialogRef<FunctionCallModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: Citation,
  ) {
    if (this.data?.namespaceType && this.data?.namespaceID) {
      this.showFullRequest = true;
      const namespaceType = this.data.namespaceType;
      const namespaceId = this.data.namespaceID;
      let namespace: NamespaceInterface = {};
      if (namespaceType === NamespaceType.ACCOUNT_GROUP) {
        namespace = {
          accountGroupNamespace: {
            accountGroupId: namespaceId,
          },
        };
      } else if (namespaceType === NamespaceType.PARTNER) {
        namespace = {
          partnerNamespace: {
            partnerId: namespaceId,
          },
        };
      } else if (namespaceType === NamespaceType.SYSTEM) {
        namespace = {
          systemNamespace: {},
        };
      } else if (namespaceType === NamespaceType.GLOBAL) {
        namespace = {
          globalNamespace: {},
        };
      }

      firstValueFrom(
        this.functionApiService.get({
          id: this.data.title,
          namespace: namespace,
        }),
      ).then((f) => {
        this.functionData = f;
        try {
          const args = JSON.parse(this.data?.functionArguments || '{}');
          delete args.Output;

          this.functionRequestBodyArgs = {};
          this.functionURLQueryArgs = {};
          this.functionHeaders = {};
          f.function.headers?.map((header: any) => {
            this.functionHeaders[header.key] = header.value;
          });

          for (const param of f.function.functionParameters || []) {
            if (args[param.name] !== undefined) {
              if (param.location === undefined) {
                this.functionRequestBodyArgs[param.name] = args[param.name];
              } else if (param.location === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM) {
                this.functionURLQueryArgs[param.name] = args[param.name];
              }
            }
          }
        } catch (e) {
          console.error('Failed to parse function arguments:', e);
          this.functionRequestBodyArgs = this.data?.functionArguments;
          this.functionURLQueryArgs = {};
        }

        this.functionCompleteURL = `${f.function.methodType} ${f.function.url}?${new URLSearchParams(this.functionURLQueryArgs).toString()}`;
      });
    } else {
      try {
        this.functionRequestBodyArgs = JSON.parse(this.data?.functionArguments);
        // The Output property is only used internally
        delete this.functionRequestBodyArgs.Output;
      } catch (e) {
        this.functionRequestBodyArgs = this.data?.functionArguments;
      }
    }

    if (this.data?.contentSize > this.data.contentSizeLimit) {
      this.contentLimitExceeded = true;
      this.contentMaxKilobytes = Math.floor(this.data.contentSizeLimit / 1024);
    } else {
      try {
        this.content = JSON.parse(this.data?.content);
      } catch (e) {
        this.content = this.data?.content;
      }
    }
  }

  copyCurlCommand(): void {
    if (!this.functionData) return;

    let curlCommand = `curl -X ${this.functionData.function.methodType} `;

    if (this.functionHeaders && Object.keys(this.functionHeaders).length > 0) {
      Object.entries(this.functionHeaders).forEach(([key, value]) => {
        curlCommand += `-H '${key}: ${value}' `;
      });
    }

    if (this.functionRequestBodyArgs && Object.keys(this.functionRequestBodyArgs).length > 0) {
      curlCommand += `-d '${JSON.stringify(this.functionRequestBodyArgs)}' `;
    }

    let url = this.functionData.function.url;
    if (this.functionURLQueryArgs && Object.keys(this.functionURLQueryArgs).length > 0) {
      url += `?${new URLSearchParams(this.functionURLQueryArgs).toString()}`;
    }

    curlCommand += `'${url}'`;

    this.clipboard.copy(curlCommand);
  }
}
