<h2 mat-dialog-title>
  <span>{{ data.title }}</span>
  <div class="subtitle">{{ data.description }}</div>
</h2>

<mat-dialog-content class="dialog-content">
  <div>
    @if (showFullRequest) {
      <div class="header-with-button">
        <h3>{{ 'INBOX.CITATIONS.FUNCTION.REQUEST_URL' | translate }}</h3>
        <button mat-button color="primary" (click)="copyCurlCommand()">
          <mat-icon class="copy-icon">content_copy</mat-icon>
          {{ 'INBOX.CITATIONS.FUNCTION.COPY_AS_CURL' | translate }}
        </button>
      </div>
      <pre><code>{{ functionCompleteURL }}</code></pre>
    }

    <h3>{{ 'INBOX.CITATIONS.FUNCTION.REQUEST_PARAMETERS' | translate }}</h3>
    <pre><code>{{ functionRequestBodyArgs | json }}</code></pre>

    @if (functionHeaders) {
      <h3>{{ 'INBOX.CITATIONS.FUNCTION.HEADERS' | translate }}</h3>
      <pre><code>{{ functionHeaders | json }}</code></pre>
    }
  </div>
  <br />
  <div>
    <h3>{{ 'INBOX.CITATIONS.FUNCTION.RESPONSE' | translate }}</h3>
    @if (contentLimitExceeded) {
      <glxy-alert type="warning">
        {{ 'INBOX.CITATIONS.FUNCTION.RESPONSE_TOO_LARGE' | translate: { maxKilobytes: this.contentMaxKilobytes } }}
      </glxy-alert>
    } @else {
      <pre>{{ content | json }}</pre>
    }
  </div>
</mat-dialog-content>
