export const METADATA_KEY_CITATIONS = 'citations';
export const METADATA_KEY_REASONING = 'reasoning';

export enum CitationType {
  TYPE_INVALID = 0,
  TYPE_USER_INPUT = 1,
  TYPE_USER_INPUT_V2 = 'userInput',
  TYPE_WEBSITE = 2,
  TYPE_WEBSITE_V2 = 'website',
  TYPE_BUSINESS_PROFILE = 3,
  TYPE_BUSINESS_PROFILE_V2 = 'businessProfile',
  TYPE_FUNCTION_CALL = 'functionCall',
}

export enum NamespaceType {
  ACCOUNT_GROUP = 'AccountGroup',
  PARTNER = 'Partner',
  SYSTEM = 'System',
  GLOBAL = 'Global',
}

export interface Citation {
  type?: CitationType;
  title: string;
  description: string;
  link: string;
  content: string;
  contentSize: number;
  contentSizeLimit: number;
  functionArguments: string;
  namespaceType: string;
  namespaceID: string;
}
